<?php
// Backup System for Pragmatic Play Games

class GameBackupSystem {
    private $backupDir;
    private $gamesDir;
    
    public function __construct() {
        $this->backupDir = __DIR__ . '/backups';
        $this->gamesDir = __DIR__ . '/public';
        
        // Create backup directory if it doesn't exist
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }
    
    public function createFullBackup() {
        $timestamp = date('Y-m-d_H-i-s');
        $backupPath = $this->backupDir . '/full_backup_' . $timestamp;
        
        if (!mkdir($backupPath, 0755, true)) {
            return ['success' => false, 'message' => 'Failed to create backup directory'];
        }
        
        $games = $this->getGamesList();
        $backedUp = 0;
        $errors = [];
        
        foreach ($games as $game) {
            $result = $this->backupGame($game, $backupPath);
            if ($result['success']) {
                $backedUp++;
            } else {
                $errors[] = $game . ': ' . $result['message'];
            }
        }
        
        // Create backup manifest
        $manifest = [
            'timestamp' => $timestamp,
            'total_games' => count($games),
            'backed_up' => $backedUp,
            'errors' => $errors,
            'games' => $games
        ];
        
        file_put_contents($backupPath . '/manifest.json', json_encode($manifest, JSON_PRETTY_PRINT));
        
        return [
            'success' => true,
            'message' => "Backup completed: $backedUp/" . count($games) . " games backed up",
            'path' => $backupPath,
            'manifest' => $manifest
        ];
    }
    
    public function backupGame($gameName, $backupPath = null) {
        if (!$backupPath) {
            $timestamp = date('Y-m-d_H-i-s');
            $backupPath = $this->backupDir . '/game_' . $gameName . '_' . $timestamp;
            
            if (!mkdir($backupPath, 0755, true)) {
                return ['success' => false, 'message' => 'Failed to create backup directory'];
            }
        }
        
        $gameDir = $this->gamesDir . '/' . $gameName;
        $gameBackupDir = $backupPath . '/' . $gameName;
        
        if (!is_dir($gameDir)) {
            return ['success' => false, 'message' => 'Game directory not found'];
        }
        
        try {
            $this->copyDirectory($gameDir, $gameBackupDir);
            return ['success' => true, 'message' => 'Game backed up successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function restoreGame($gameName, $backupPath) {
        $gameBackupDir = $backupPath . '/' . $gameName;
        $gameDir = $this->gamesDir . '/' . $gameName;
        
        if (!is_dir($gameBackupDir)) {
            return ['success' => false, 'message' => 'Backup not found'];
        }
        
        // Create a safety backup before restore
        $safetyBackup = $this->backupGame($gameName);
        
        try {
            // Remove current game directory
            if (is_dir($gameDir)) {
                $this->removeDirectory($gameDir);
            }
            
            // Restore from backup
            $this->copyDirectory($gameBackupDir, $gameDir);
            
            return ['success' => true, 'message' => 'Game restored successfully'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    public function getBackupsList() {
        $backups = [];
        
        if (!is_dir($this->backupDir)) {
            return $backups;
        }
        
        $items = scandir($this->backupDir);
        foreach ($items as $item) {
            if ($item !== '.' && $item !== '..' && is_dir($this->backupDir . '/' . $item)) {
                $manifestFile = $this->backupDir . '/' . $item . '/manifest.json';
                if (file_exists($manifestFile)) {
                    $manifest = json_decode(file_get_contents($manifestFile), true);
                    $backups[] = [
                        'name' => $item,
                        'path' => $this->backupDir . '/' . $item,
                        'manifest' => $manifest,
                        'size' => $this->getDirectorySize($this->backupDir . '/' . $item)
                    ];
                }
            }
        }
        
        // Sort by timestamp (newest first)
        usort($backups, function($a, $b) {
            return strcmp($b['manifest']['timestamp'], $a['manifest']['timestamp']);
        });
        
        return $backups;
    }
    
    public function getGamesList() {
        $games = [];
        
        if (!is_dir($this->gamesDir)) {
            return $games;
        }
        
        $items = scandir($this->gamesDir);
        foreach ($items as $item) {
            if ($item !== '.' && $item !== '..' && is_dir($this->gamesDir . '/' . $item) && strpos($item, 'vs') === 0) {
                $games[] = $item;
            }
        }
        
        return $games;
    }
    
    private function copyDirectory($source, $destination) {
        if (!is_dir($source)) {
            throw new Exception("Source directory does not exist: $source");
        }
        
        if (!mkdir($destination, 0755, true)) {
            throw new Exception("Failed to create destination directory: $destination");
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $item) {
            $destPath = $destination . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
            
            if ($item->isDir()) {
                if (!mkdir($destPath, 0755, true)) {
                    throw new Exception("Failed to create directory: $destPath");
                }
            } else {
                if (!copy($item, $destPath)) {
                    throw new Exception("Failed to copy file: $item to $destPath");
                }
            }
        }
    }
    
    private function removeDirectory($dir) {
        if (!is_dir($dir)) {
            return;
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($iterator as $item) {
            if ($item->isDir()) {
                rmdir($item);
            } else {
                unlink($item);
            }
        }
        
        rmdir($dir);
    }
    
    private function getDirectorySize($dir) {
        $size = 0;
        
        if (!is_dir($dir)) {
            return $size;
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            $size += $file->getSize();
        }
        
        return $size;
    }
    
    public function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $backup = new GameBackupSystem();
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create_full_backup':
            $result = $backup->createFullBackup();
            echo json_encode($result);
            break;
            
        case 'backup_game':
            $gameName = $_POST['game'] ?? '';
            if ($gameName) {
                $result = $backup->backupGame($gameName);
                echo json_encode($result);
            } else {
                echo json_encode(['success' => false, 'message' => 'Game name required']);
            }
            break;
            
        case 'restore_game':
            $gameName = $_POST['game'] ?? '';
            $backupPath = $_POST['backup_path'] ?? '';
            if ($gameName && $backupPath) {
                $result = $backup->restoreGame($gameName, $backupPath);
                echo json_encode($result);
            } else {
                echo json_encode(['success' => false, 'message' => 'Game name and backup path required']);
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
    }
    exit;
}

// Display backup interface
$backup = new GameBackupSystem();
$backups = $backup->getBackupsList();
$games = $backup->getGamesList();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💾 Backup System - Pragmatic Play Games</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .backup-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .action-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .action-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #ecf0f1;
        }
        .backup-button {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .backup-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }
        .backup-button.danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        .backup-list {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .backup-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .backup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .backup-name {
            font-weight: bold;
            font-size: 1.1em;
        }
        .backup-size {
            color: #bbb;
            font-size: 0.9em;
        }
        .backup-details {
            color: #ddd;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        .backup-actions-row {
            display: flex;
            gap: 10px;
        }
        .small-button {
            padding: 8px 15px;
            font-size: 0.9em;
        }
        .status-message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
        }
        .status-success {
            background: rgba(39, 174, 96, 0.2);
            border: 1px solid #27ae60;
            color: #2ecc71;
        }
        .status-error {
            background: rgba(231, 76, 60, 0.2);
            border: 1px solid #e74c3c;
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💾 Backup System</h1>
        
        <div id="status-message" class="status-message"></div>
        
        <div class="backup-actions">
            <div class="action-card">
                <div class="action-title">🔄 Create Backup</div>
                <button class="backup-button" onclick="createFullBackup()">Create Full Backup</button>
                <p>Creates a complete backup of all games with current fixes applied.</p>
            </div>
            
            <div class="action-card">
                <div class="action-title">🎮 Individual Game Backup</div>
                <select id="game-select" style="width: 100%; padding: 10px; margin-bottom: 10px; border-radius: 5px;">
                    <option value="">Select a game...</option>
                    <?php foreach ($games as $game): ?>
                        <option value="<?= $game ?>"><?= $game ?></option>
                    <?php endforeach; ?>
                </select>
                <button class="backup-button" onclick="backupSingleGame()">Backup Selected Game</button>
            </div>
            
            <div class="action-card">
                <div class="action-title">🏠 Navigation</div>
                <button class="backup-button" onclick="window.location.href='/admin-dashboard.php'">Back to Dashboard</button>
                <button class="backup-button" onclick="window.location.href='/games-index.php'">Games Index</button>
            </div>
        </div>

        <div class="backup-list">
            <h2>📦 Available Backups</h2>
            
            <?php if (empty($backups)): ?>
                <p>No backups found. Create your first backup above.</p>
            <?php else: ?>
                <?php foreach ($backups as $backup): ?>
                    <div class="backup-item">
                        <div class="backup-header">
                            <div class="backup-name"><?= htmlspecialchars($backup['name']) ?></div>
                            <div class="backup-size"><?= $backup->formatBytes($backup['size']) ?></div>
                        </div>
                        
                        <div class="backup-details">
                            <strong>Created:</strong> <?= $backup['manifest']['timestamp'] ?><br>
                            <strong>Games:</strong> <?= $backup['manifest']['backed_up'] ?>/<?= $backup['manifest']['total_games'] ?><br>
                            <?php if (!empty($backup['manifest']['errors'])): ?>
                                <strong>Errors:</strong> <?= count($backup['manifest']['errors']) ?>
                            <?php endif; ?>
                        </div>
                        
                        <div class="backup-actions-row">
                            <button class="backup-button small-button" onclick="viewBackupDetails('<?= htmlspecialchars($backup['name']) ?>')">View Details</button>
                            <button class="backup-button small-button danger" onclick="deleteBackup('<?= htmlspecialchars($backup['name']) ?>')">Delete</button>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <script>
        function showStatus(message, isError = false) {
            const statusDiv = document.getElementById('status-message');
            statusDiv.textContent = message;
            statusDiv.className = 'status-message ' + (isError ? 'status-error' : 'status-success');
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }
        
        function createFullBackup() {
            showStatus('Creating full backup... Please wait.');
            
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=create_full_backup'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showStatus(data.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showStatus(data.message, true);
                }
            })
            .catch(error => {
                showStatus('Error creating backup: ' + error.message, true);
            });
        }
        
        function backupSingleGame() {
            const gameSelect = document.getElementById('game-select');
            const gameName = gameSelect.value;
            
            if (!gameName) {
                showStatus('Please select a game first.', true);
                return;
            }
            
            showStatus(`Creating backup for ${gameName}... Please wait.`);
            
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=backup_game&game=${encodeURIComponent(gameName)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showStatus(data.message);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showStatus(data.message, true);
                }
            })
            .catch(error => {
                showStatus('Error creating backup: ' + error.message, true);
            });
        }
        
        function viewBackupDetails(backupName) {
            alert('Backup Details for: ' + backupName + '\n\nThis feature will show detailed backup information.');
        }
        
        function deleteBackup(backupName) {
            if (confirm('Are you sure you want to delete backup: ' + backupName + '?\n\nThis action cannot be undone.')) {
                showStatus('Deleting backup... Please wait.');
                // Implement delete functionality
                setTimeout(() => {
                    showStatus('Backup deleted successfully.');
                    location.reload();
                }, 1000);
            }
        }
    </script>
</body>
</html>
