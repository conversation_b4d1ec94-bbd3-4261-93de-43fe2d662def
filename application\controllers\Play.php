<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Play extends CI_Controller {

    public function index()
    {
        $game = $this->input->get('game');
        $lang = $this->input->get('lang', TRUE) ?: 'en';
        $cur = $this->input->get('cur', TRUE) ?: 'USD';
        $user = $this->input->get('user', TRUE) ?: 'demo';

        $gameMapping = $this->getGameMapping();

        if (!$game || !isset($gameMapping[$game])) {
            show_404();
            return;
        }

        $gameInfo = $gameMapping[$game];
        $gamePath = FCPATH . $gameInfo['path'];

        if (!file_exists($gamePath)) {
            show_404();
            return;
        }

        $data = [
            'game' => $game,
            'gameName' => $gameInfo['name'],
            'gamePath' => $gameInfo['path'],
            'lang' => $lang,
            'cur' => $cur,
            'user' => $user,
            'session' => 'session_' . uniqid(),
            'api_exit' => $this->input->get('api_exit', TRUE) ?: '/'
        ];

        $this->load->view('game', $data);
    }

    private function getGameMapping() {
        return [
            'vs20olympgate' => [
                'name' => 'Gates of Olympus',
                'path' => 'vs20olympgate/gs2c/html5Game.php'
            ],
            'vs10bbbonanza' => [
                'name' => 'Big Bass Bonanza',
                'path' => 'vs10bbbonanza/gs2c/html5Game.php'
            ],
            'vs20starlight' => [
                'name' => 'Starlight Princess',
                'path' => 'vs20starlight/gs2c/html5Game.php'
            ],
            'vs20doghouse' => [
                'name' => 'The Dog House',
                'path' => 'vs20doghouse/gs2c/html5Game.php'
            ],
            'vs20sugarrush' => [
                'name' => 'Sugar Rush',
                'path' => 'vs20sugarrush/gs2c/html5Game.php'
            ],
            'vs25wolfgold' => [
                'name' => 'Wolf Gold',
                'path' => 'vs25wolfgold/gs2c/html5Game.php'
            ],
            'vs20sbxmas' => [
                'name' => 'Sweet Bonanza Xmas',
                'path' => 'vs20sbxmas/gs2c/html5Game.php'
            ],
            'vs20fparty2' => [
                'name' => 'Fruit Party 2',
                'path' => 'vs20fparty2/gs2c/html5Game.php'
            ],
            'vs20muertos' => [
                'name' => 'Day of Dead',
                'path' => 'vs20muertos/gs2c/html5Game.php'
            ],
            'vs20cleocatra' => [
                'name' => 'Cleocatra',
                'path' => 'vs20cleocatra/gs2c/html5Game.php'
            ],
            'vs25chilli' => [
                'name' => 'Chilli Heat',
                'path' => 'vs25chilli/gs2c/html5Game.php'
            ],
            'vs10madame' => [
                'name' => 'Madame Destiny',
                'path' => 'vs10madame/gs2c/html5Game.php'
            ],
            'vs10eyestorm' => [
                'name' => 'Eye of the Storm',
                'path' => 'vs10eyestorm/gs2c/html5Game.php'
            ],
            'vs20wildpix' => [
                'name' => 'Wild Pixies',
                'path' => 'vs20wildpix/gs2c/html5Game.php'
            ],
            'vs20egypt' => [
                'name' => 'Ancient Egypt Classic',
                'path' => 'vs20egypt/gs2c/html5Game.php'
            ],
            'vs25copsrobbers' => [
                'name' => 'Cops and Robbers',
                'path' => 'vs25copsrobbers/gs2c/html5Game.php'
            ],
            'vs20goldfever' => [
                'name' => 'Gold Fever',
                'path' => 'vs20goldfever/gs2c/html5Game.php'
            ],
            'vs5super7' => [
                'name' => 'Super 7s',
                'path' => 'vs5super7/gs2c/html5Game.php'
            ],
            'vs5luckytig' => [
                'name' => 'Lucky Tiger',
                'path' => 'vs5luckytig/gs2c/html5Game.php'
            ]
        ];
    }
}
