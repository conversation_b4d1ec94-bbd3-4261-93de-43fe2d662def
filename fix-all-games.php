<?php
// <PERSON>ript to fix all Pragmatic Play games

function getGameFixes($gameName) {
    return '
    <!-- GAME FIXES - MUST LOAD FIRST -->
    <script>
        // Critical fixes that must load before any other scripts
        console.log("Injecting game fixes for ' . $gameName . '");
        
        // Define qstr function IMMEDIATELY
        window.qstr = function(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name) || "";
        };
        
        // Add currency property to qstr for compatibility
        window.qstr.cur = "USD";
        
        // Make qstr globally available
        if (typeof qstr === "undefined") {
            window.qstr = window.qstr;
        }
        
        // WURFL Mock
        window.WURFL = {
            is_mobile: false,
            form_factor: "Desktop",
            complete_device_name: "Desktop Browser",
            brand_name: "Generic",
            model_name: "Browser",
            marketing_name: "Desktop Browser",
            device_os: "Windows",
            device_os_version: "10",
            pointing_method: "mouse",
            resolution_width: 1920,
            resolution_height: 1080,
            max_image_width: 1920,
            max_image_height: 1080,
            canvas_support: "html5",
            ajax_support_javascript: true,
            cookie_support: true,
            javascript_support: true
        };
        
        // loadStyles function
        window.loadStyles = function(styles) {
            if (typeof styles === "string") {
                const link = document.createElement("link");
                link.rel = "stylesheet";
                link.href = styles;
                document.head.appendChild(link);
            } else if (Array.isArray(styles)) {
                styles.forEach(style => {
                    const link = document.createElement("link");
                    link.rel = "stylesheet";
                    link.href = style;
                    document.head.appendChild(link);
                });
            }
        };
        
        // Game configuration
        window.gameConfig = {
            serverUrl: window.location.origin + "/game/' . $gameName . '/server",
            sessionId: "session_" + Math.random().toString(36).substr(2, 9),
            currency: "USD",
            language: "en",
            gameName: "' . $gameName . '"
        };
        
        // Fix AudioContext
        document.addEventListener("click", function() {
            if (window.AudioContext || window.webkitAudioContext) {
                const AudioContext = window.AudioContext || window.webkitAudioContext;
                if (window.audioContext && window.audioContext.state === "suspended") {
                    window.audioContext.resume();
                }
            }
        }, { once: true });
        
        // Override XMLHttpRequest to handle server calls
        const originalXHR = window.XMLHttpRequest;
        window.XMLHttpRequest = function() {
            const xhr = new originalXHR();
            const originalOpen = xhr.open;
            
            xhr.open = function(method, url, async, user, password) {
                // Redirect server calls to our mock server
                if (url.includes("/server") && !url.includes(window.location.origin)) {
                    url = window.location.origin + "/game/' . $gameName . '/server";
                }
                return originalOpen.call(this, method, url, async, user, password);
            };
            
            return xhr;
        };
        
        console.log("Game fixes injected successfully for ' . $gameName . '");
    </script>';
}

// Get all game directories
$gamesDir = __DIR__ . '/public';
$games = [];

if (is_dir($gamesDir)) {
    $gameDirectories = scandir($gamesDir);
    foreach ($gameDirectories as $dir) {
        if ($dir !== '.' && $dir !== '..' && is_dir($gamesDir . '/' . $dir) && strpos($dir, 'vs') === 0) {
            // Check if the game has the required structure
            $htmlFile = $gamesDir . '/' . $dir . '/gs2c/html5Game.php';
            if (file_exists($htmlFile)) {
                $games[] = $dir;
            }
        }
    }
}

echo "<h1>Fix All Pragmatic Play Games</h1>";
echo "<p>Found " . count($games) . " games:</p>";

$fixed = 0;
$skipped = 0;
$errors = 0;

foreach ($games as $gameName) {
    echo "<h3>Processing: $gameName</h3>";
    
    $htmlFile = $gamesDir . '/' . $gameName . '/gs2c/html5Game.php';
    
    if (file_exists($htmlFile)) {
        $content = file_get_contents($htmlFile);
        
        // Check if fixes are already applied
        if (strpos($content, 'GAME FIXES - MUST LOAD FIRST') !== false) {
            echo "<p>✅ $gameName - Fixes already applied</p>";
            $skipped++;
            continue;
        }
        
        // Find the title tag to inject after it
        $titlePattern = '/<title>([^<]+)<\/title>\s*<meta name="google" content="nopagereadaloud" \/>/';
        
        if (preg_match($titlePattern, $content, $matches)) {
            $fixes = getGameFixes($gameName);
            $replacement = $matches[0] . $fixes;
            $newContent = preg_replace($titlePattern, $replacement, $content);
            
            // Create backup
            $backupFile = $htmlFile . '.backup';
            if (!file_exists($backupFile)) {
                copy($htmlFile, $backupFile);
                echo "<p>📁 Created backup: $backupFile</p>";
            }
            
            // Write the modified content
            if (file_put_contents($htmlFile, $newContent)) {
                echo "<p>✅ $gameName - Fixes applied successfully</p>";
                $fixed++;
            } else {
                echo "<p>❌ $gameName - Failed to write fixes</p>";
                $errors++;
            }
        } else {
            echo "<p>⚠️ $gameName - Could not find injection point</p>";
            $errors++;
        }
    } else {
        echo "<p>❌ $gameName - html5Game.php not found</p>";
        $errors++;
    }
}

echo "<h2>✅ Game fixes application completed!</h2>";
echo "<p><strong>Summary:</strong></p>";
echo "<ul>";
echo "<li>✅ Fixed: $fixed games</li>";
echo "<li>⏭️ Skipped: $skipped games (already fixed)</li>";
echo "<li>❌ Errors: $errors games</li>";
echo "</ul>";
echo "<p>All games should now load without JavaScript errors.</p>";

// Generate game links
echo "<h2>🎮 Available Games:</h2>";
foreach ($games as $gameName) {
    $gameTitle = ucwords(str_replace(['vs', 'vs20', 'vs25', 'vs10', 'vs5'], '', $gameName));
    echo "<p><a href='/game/$gameName/prego?api_exit=/' target='_blank'>🎰 $gameTitle ($gameName)</a></p>";
}
?>
