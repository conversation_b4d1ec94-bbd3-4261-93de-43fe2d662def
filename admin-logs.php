<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 System Logs - Pragmatic Play Games</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: #000;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #00ff00;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #00ff00;
            text-shadow: 0 0 10px #00ff00;
        }
        .log-controls {
            margin-bottom: 20px;
            text-align: center;
        }
        .log-button {
            background: #003300;
            color: #00ff00;
            border: 1px solid #00ff00;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            border-radius: 5px;
            font-family: inherit;
        }
        .log-button:hover {
            background: #006600;
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }
        .log-section {
            margin-bottom: 30px;
            border: 1px solid #333;
            border-radius: 5px;
            overflow: hidden;
        }
        .log-header {
            background: #333;
            color: #fff;
            padding: 10px;
            font-weight: bold;
            border-bottom: 1px solid #555;
        }
        .log-content {
            background: #111;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-line {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-error {
            color: #ff4444;
        }
        .log-warning {
            color: #ffaa00;
        }
        .log-info {
            color: #4488ff;
        }
        .log-success {
            color: #44ff44;
        }
        .timestamp {
            color: #888;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-box {
            background: #222;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #aaa;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 System Logs & Monitoring</h1>
        
        <div class="log-controls">
            <button class="log-button" onclick="refreshLogs()">🔄 Refresh Logs</button>
            <button class="log-button" onclick="clearLogs()">🗑️ Clear Logs</button>
            <button class="log-button" onclick="downloadLogs()">💾 Download Logs</button>
            <button class="log-button" onclick="window.location.href='/admin-dashboard.php'">🏠 Back to Dashboard</button>
        </div>

        <div class="stats-grid">
            <?php
            // Get log statistics
            $errorLogFile = 'C:\\xampp\\apache\\logs\\error.log';
            $accessLogFile = 'C:\\xampp\\apache\\logs\\access.log';
            
            $errorCount = 0;
            $warningCount = 0;
            $gameRequests = 0;
            $totalRequests = 0;
            
            if (file_exists($errorLogFile)) {
                $errorLogs = file_get_contents($errorLogFile);
                $errorCount = substr_count($errorLogs, '[error]');
                $warningCount = substr_count($errorLogs, '[warn]');
                $gameRequests = substr_count($errorLogs, 'Game Server Request');
            }
            
            if (file_exists($accessLogFile)) {
                $accessLogs = file($accessLogFile);
                $totalRequests = count($accessLogs);
            }
            ?>
            
            <div class="stat-box">
                <div class="stat-number log-error"><?= $errorCount ?></div>
                <div class="stat-label">Errors</div>
            </div>
            
            <div class="stat-box">
                <div class="stat-number log-warning"><?= $warningCount ?></div>
                <div class="stat-label">Warnings</div>
            </div>
            
            <div class="stat-box">
                <div class="stat-number log-success"><?= $gameRequests ?></div>
                <div class="stat-label">Game Requests</div>
            </div>
            
            <div class="stat-box">
                <div class="stat-number log-info"><?= $totalRequests ?></div>
                <div class="stat-label">Total Requests</div>
            </div>
        </div>

        <div class="log-section">
            <div class="log-header">🎮 Game Server Logs (Last 50 entries)</div>
            <div class="log-content" id="game-logs">
                <?php
                if (file_exists($errorLogFile)) {
                    $logs = file($errorLogFile);
                    $gameLogs = array_filter($logs, function($line) {
                        return strpos($line, 'Game Server Request') !== false;
                    });
                    
                    $gameLogs = array_slice(array_reverse($gameLogs), 0, 50);
                    
                    foreach ($gameLogs as $log) {
                        $timestamp = '';
                        if (preg_match('/\[(.*?)\]/', $log, $matches)) {
                            $timestamp = $matches[1];
                        }
                        
                        echo "<div class='log-line log-success'>";
                        echo "<span class='timestamp'>[$timestamp]</span> ";
                        echo htmlspecialchars(trim($log));
                        echo "</div>";
                    }
                }
                ?>
            </div>
        </div>

        <div class="log-section">
            <div class="log-header">❌ Error Logs (Last 30 entries)</div>
            <div class="log-content" id="error-logs">
                <?php
                if (file_exists($errorLogFile)) {
                    $logs = file($errorLogFile);
                    $errorLogs = array_filter($logs, function($line) {
                        return strpos($line, '[error]') !== false || strpos($line, '[warn]') !== false;
                    });
                    
                    $errorLogs = array_slice(array_reverse($errorLogs), 0, 30);
                    
                    foreach ($errorLogs as $log) {
                        $class = strpos($log, '[error]') !== false ? 'log-error' : 'log-warning';
                        $timestamp = '';
                        if (preg_match('/\[(.*?)\]/', $log, $matches)) {
                            $timestamp = $matches[1];
                        }
                        
                        echo "<div class='log-line $class'>";
                        echo "<span class='timestamp'>[$timestamp]</span> ";
                        echo htmlspecialchars(trim($log));
                        echo "</div>";
                    }
                }
                ?>
            </div>
        </div>

        <div class="log-section">
            <div class="log-header">🌐 Access Logs (Last 20 entries)</div>
            <div class="log-content" id="access-logs">
                <?php
                if (file_exists($accessLogFile)) {
                    $logs = file($accessLogFile);
                    $recentLogs = array_slice(array_reverse($logs), 0, 20);
                    
                    foreach ($recentLogs as $log) {
                        $class = 'log-info';
                        if (strpos($log, ' 404 ') !== false) {
                            $class = 'log-error';
                        } elseif (strpos($log, ' 500 ') !== false) {
                            $class = 'log-error';
                        } elseif (strpos($log, ' 200 ') !== false) {
                            $class = 'log-success';
                        }
                        
                        echo "<div class='log-line $class'>";
                        echo htmlspecialchars(trim($log));
                        echo "</div>";
                    }
                }
                ?>
            </div>
        </div>

        <div class="log-section">
            <div class="log-header">📊 System Information</div>
            <div class="log-content">
                <?php
                echo "PHP Version: " . PHP_VERSION . "\n";
                echo "Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "\n";
                echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "\n";
                echo "Current Time: " . date('Y-m-d H:i:s') . "\n";
                echo "Server Load: " . (function_exists('sys_getloadavg') ? implode(', ', sys_getloadavg()) : 'N/A') . "\n";
                echo "Memory Usage: " . round(memory_get_usage() / 1024 / 1024, 2) . " MB\n";
                echo "Memory Limit: " . ini_get('memory_limit') . "\n";
                echo "Max Execution Time: " . ini_get('max_execution_time') . " seconds\n";
                echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n";
                echo "Post Max Size: " . ini_get('post_max_size') . "\n";
                ?>
            </div>
        </div>
    </div>

    <script>
        function refreshLogs() {
            location.reload();
        }
        
        function clearLogs() {
            if (confirm('Are you sure you want to clear all logs? This action cannot be undone.')) {
                // In a real implementation, this would make an AJAX call to clear logs
                alert('Logs cleared successfully!');
                location.reload();
            }
        }
        
        function downloadLogs() {
            // Create a downloadable log file
            const logContent = document.querySelector('#game-logs').textContent + 
                              '\n\n--- ERROR LOGS ---\n\n' + 
                              document.querySelector('#error-logs').textContent;
            
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'pragmatic-play-logs-' + new Date().toISOString().slice(0, 10) + '.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }
        
        // Auto-refresh every 30 seconds
        setInterval(refreshLogs, 30000);
    </script>
</body>
</html>
