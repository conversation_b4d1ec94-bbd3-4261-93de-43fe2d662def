<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Real-Time Monitor - Pragmatic Play Games</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            color: #00d4ff;
        }
        .monitor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .monitor-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }
        .monitor-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #00d4ff, #0099cc);
        }
        .card-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #00d4ff;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
            animation: pulse 2s infinite;
        }
        .status-indicator.warning {
            background: #f39c12;
        }
        .status-indicator.error {
            background: #e74c3c;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .metric-label {
            color: #bbb;
        }
        .metric-value {
            font-weight: bold;
            color: #00d4ff;
        }
        .chart-container {
            height: 200px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            margin-top: 15px;
            position: relative;
            overflow: hidden;
        }
        .chart-bar {
            position: absolute;
            bottom: 0;
            background: linear-gradient(to top, #00d4ff, #0099cc);
            border-radius: 2px 2px 0 0;
            transition: height 0.5s ease;
        }
        .activity-log {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .log-entry {
            margin-bottom: 8px;
            padding: 5px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.05);
        }
        .log-timestamp {
            color: #888;
            margin-right: 10px;
        }
        .log-success {
            border-left: 3px solid #27ae60;
        }
        .log-warning {
            border-left: 3px solid #f39c12;
        }
        .log-error {
            border-left: 3px solid #e74c3c;
        }
        .controls {
            text-align: center;
            margin-bottom: 20px;
        }
        .control-button {
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .control-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }
        .control-button.active {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        .game-status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
        }
        .game-status-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .game-name {
            font-weight: bold;
            margin-bottom: 10px;
            color: #ecf0f1;
        }
        .game-metrics {
            display: flex;
            justify-content: space-around;
            margin-top: 10px;
        }
        .game-metric {
            text-align: center;
        }
        .game-metric-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #00d4ff;
        }
        .game-metric-label {
            font-size: 0.8em;
            color: #bbb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Real-Time System Monitor</h1>
        
        <div class="controls">
            <button class="control-button active" id="auto-refresh-btn" onclick="toggleAutoRefresh()">🔄 Auto Refresh: ON</button>
            <button class="control-button" onclick="refreshData()">🔄 Refresh Now</button>
            <button class="control-button" onclick="window.location.href='/admin-dashboard.php'">🏠 Dashboard</button>
            <button class="control-button" onclick="window.location.href='/admin-logs.php'">📋 Logs</button>
        </div>

        <div class="monitor-grid">
            <div class="monitor-card">
                <div class="card-title">
                    <div class="status-indicator" id="system-status"></div>
                    🖥️ System Status
                </div>
                <div class="metric">
                    <span class="metric-label">Server Status</span>
                    <span class="metric-value" id="server-status">Online</span>
                </div>
                <div class="metric">
                    <span class="metric-label">PHP Version</span>
                    <span class="metric-value"><?= PHP_VERSION ?></span>
                </div>
                <div class="metric">
                    <span class="metric-label">Memory Usage</span>
                    <span class="metric-value" id="memory-usage"><?= round(memory_get_usage() / 1024 / 1024, 2) ?> MB</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Uptime</span>
                    <span class="metric-value" id="uptime">--</span>
                </div>
            </div>

            <div class="monitor-card">
                <div class="card-title">
                    <div class="status-indicator" id="games-status"></div>
                    🎮 Games Status
                </div>
                <div class="metric">
                    <span class="metric-label">Total Games</span>
                    <span class="metric-value" id="total-games">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Fixed Games</span>
                    <span class="metric-value" id="fixed-games">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Active Sessions</span>
                    <span class="metric-value" id="active-sessions">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Success Rate</span>
                    <span class="metric-value" id="success-rate">--</span>
                </div>
            </div>

            <div class="monitor-card">
                <div class="card-title">
                    <div class="status-indicator" id="requests-status"></div>
                    📊 Request Statistics
                </div>
                <div class="metric">
                    <span class="metric-label">Requests/Min</span>
                    <span class="metric-value" id="requests-per-min">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Game Requests</span>
                    <span class="metric-value" id="game-requests">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Error Rate</span>
                    <span class="metric-value" id="error-rate">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Avg Response</span>
                    <span class="metric-value" id="avg-response">--</span>
                </div>
                <div class="chart-container" id="requests-chart"></div>
            </div>

            <div class="monitor-card">
                <div class="card-title">
                    <div class="status-indicator" id="performance-status"></div>
                    ⚡ Performance Metrics
                </div>
                <div class="metric">
                    <span class="metric-label">CPU Usage</span>
                    <span class="metric-value" id="cpu-usage">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Disk Usage</span>
                    <span class="metric-value" id="disk-usage">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Network I/O</span>
                    <span class="metric-value" id="network-io">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Load Average</span>
                    <span class="metric-value" id="load-average">--</span>
                </div>
                <div class="chart-container" id="performance-chart"></div>
            </div>
        </div>

        <div class="monitor-grid">
            <div class="monitor-card" style="grid-column: 1 / -1;">
                <div class="card-title">
                    <div class="status-indicator"></div>
                    🎰 Individual Game Status
                </div>
                <div class="game-status-grid" id="game-status-grid">
                    <!-- Game status cards will be populated here -->
                </div>
            </div>
        </div>

        <div class="monitor-grid">
            <div class="monitor-card" style="grid-column: 1 / -1;">
                <div class="card-title">
                    <div class="status-indicator"></div>
                    📝 Live Activity Log
                </div>
                <div class="activity-log" id="activity-log">
                    <!-- Live log entries will appear here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        let autoRefresh = true;
        let refreshInterval;
        let requestsData = [];
        let performanceData = [];

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            const btn = document.getElementById('auto-refresh-btn');
            
            if (autoRefresh) {
                btn.textContent = '🔄 Auto Refresh: ON';
                btn.classList.add('active');
                startAutoRefresh();
            } else {
                btn.textContent = '🔄 Auto Refresh: OFF';
                btn.classList.remove('active');
                stopAutoRefresh();
            }
        }

        function startAutoRefresh() {
            refreshInterval = setInterval(refreshData, 5000); // Refresh every 5 seconds
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }

        function refreshData() {
            updateSystemMetrics();
            updateGameStatus();
            updateRequestStats();
            updatePerformanceMetrics();
            updateActivityLog();
        }

        function updateSystemMetrics() {
            // Simulate system metrics updates
            const memoryUsage = Math.random() * 100 + 50;
            document.getElementById('memory-usage').textContent = memoryUsage.toFixed(2) + ' MB';
            
            const uptime = Math.floor(Date.now() / 1000 / 60); // Minutes since page load
            document.getElementById('uptime').textContent = uptime + ' min';
            
            // Update status indicator
            const statusIndicator = document.getElementById('system-status');
            statusIndicator.className = 'status-indicator';
        }

        function updateGameStatus() {
            // Simulate game status updates
            const totalGames = 25;
            const fixedGames = Math.floor(Math.random() * 5) + 20;
            const activeSessions = Math.floor(Math.random() * 50) + 10;
            const successRate = Math.floor((fixedGames / totalGames) * 100);
            
            document.getElementById('total-games').textContent = totalGames;
            document.getElementById('fixed-games').textContent = fixedGames;
            document.getElementById('active-sessions').textContent = activeSessions;
            document.getElementById('success-rate').textContent = successRate + '%';
            
            // Update games grid
            updateGameStatusGrid();
        }

        function updateGameStatusGrid() {
            const games = [
                'vs20olympgate', 'vs20wildboost', 'vs10bbbonanza', 'vs20starlight',
                'vs20doghouse', 'vs20sugarrush', 'vs25wolfgold', 'vs20sbxmas'
            ];
            
            const gameNames = {
                'vs20olympgate': 'Gates of Olympus',
                'vs20wildboost': 'Wild Booster',
                'vs10bbbonanza': 'Big Bass Bonanza',
                'vs20starlight': 'Starlight Princess',
                'vs20doghouse': 'The Dog House',
                'vs20sugarrush': 'Sugar Rush',
                'vs25wolfgold': 'Wolf Gold',
                'vs20sbxmas': 'Sweet Bonanza Xmas'
            };
            
            const grid = document.getElementById('game-status-grid');
            grid.innerHTML = '';
            
            games.forEach(game => {
                const sessions = Math.floor(Math.random() * 10) + 1;
                const requests = Math.floor(Math.random() * 100) + 50;
                const errors = Math.floor(Math.random() * 3);
                
                const card = document.createElement('div');
                card.className = 'game-status-card';
                card.innerHTML = `
                    <div class="game-name">${gameNames[game] || game}</div>
                    <div class="status-indicator ${errors > 0 ? 'warning' : ''}"></div>
                    <div class="game-metrics">
                        <div class="game-metric">
                            <div class="game-metric-value">${sessions}</div>
                            <div class="game-metric-label">Sessions</div>
                        </div>
                        <div class="game-metric">
                            <div class="game-metric-value">${requests}</div>
                            <div class="game-metric-label">Requests</div>
                        </div>
                        <div class="game-metric">
                            <div class="game-metric-value">${errors}</div>
                            <div class="game-metric-label">Errors</div>
                        </div>
                    </div>
                `;
                grid.appendChild(card);
            });
        }

        function updateRequestStats() {
            const requestsPerMin = Math.floor(Math.random() * 50) + 20;
            const gameRequests = Math.floor(Math.random() * 200) + 100;
            const errorRate = (Math.random() * 5).toFixed(2);
            const avgResponse = (Math.random() * 200 + 50).toFixed(0);
            
            document.getElementById('requests-per-min').textContent = requestsPerMin;
            document.getElementById('game-requests').textContent = gameRequests;
            document.getElementById('error-rate').textContent = errorRate + '%';
            document.getElementById('avg-response').textContent = avgResponse + 'ms';
            
            // Update requests chart
            requestsData.push(requestsPerMin);
            if (requestsData.length > 20) requestsData.shift();
            updateChart('requests-chart', requestsData);
        }

        function updatePerformanceMetrics() {
            const cpuUsage = (Math.random() * 30 + 10).toFixed(1);
            const diskUsage = (Math.random() * 20 + 60).toFixed(1);
            const networkIO = (Math.random() * 100 + 50).toFixed(0);
            const loadAverage = (Math.random() * 2 + 0.5).toFixed(2);
            
            document.getElementById('cpu-usage').textContent = cpuUsage + '%';
            document.getElementById('disk-usage').textContent = diskUsage + '%';
            document.getElementById('network-io').textContent = networkIO + ' KB/s';
            document.getElementById('load-average').textContent = loadAverage;
            
            // Update performance chart
            performanceData.push(parseFloat(cpuUsage));
            if (performanceData.length > 20) performanceData.shift();
            updateChart('performance-chart', performanceData);
        }

        function updateChart(containerId, data) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            
            const maxValue = Math.max(...data);
            const barWidth = container.offsetWidth / data.length - 2;
            
            data.forEach((value, index) => {
                const bar = document.createElement('div');
                bar.className = 'chart-bar';
                bar.style.left = (index * (barWidth + 2)) + 'px';
                bar.style.width = barWidth + 'px';
                bar.style.height = (value / maxValue * 180) + 'px';
                container.appendChild(bar);
            });
        }

        function updateActivityLog() {
            const log = document.getElementById('activity-log');
            const timestamp = new Date().toLocaleTimeString();
            
            const activities = [
                { type: 'success', message: 'Game server request processed successfully' },
                { type: 'success', message: 'Player session initialized' },
                { type: 'warning', message: 'High memory usage detected' },
                { type: 'success', message: 'Backup completed successfully' },
                { type: 'success', message: 'Game fixes applied automatically' }
            ];
            
            if (Math.random() > 0.7) { // 30% chance to add new log entry
                const activity = activities[Math.floor(Math.random() * activities.length)];
                const entry = document.createElement('div');
                entry.className = `log-entry log-${activity.type}`;
                entry.innerHTML = `
                    <span class="log-timestamp">${timestamp}</span>
                    ${activity.message}
                `;
                
                log.insertBefore(entry, log.firstChild);
                
                // Keep only last 20 entries
                while (log.children.length > 20) {
                    log.removeChild(log.lastChild);
                }
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            if (autoRefresh) {
                startAutoRefresh();
            }
        });
    </script>
</body>
</html>
