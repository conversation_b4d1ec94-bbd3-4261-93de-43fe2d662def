<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎰 Admin Dashboard - Pragmatic Play Games</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
        }
        .stat-label {
            font-size: 1.1em;
            opacity: 0.8;
        }
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .action-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .action-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #ecf0f1;
        }
        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
            border: none;
            cursor: pointer;
        }
        .action-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
            text-decoration: none;
            color: white;
        }
        .action-button.success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        .action-button.warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }
        .action-button.danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        .games-table {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: bold;
        }
        .status-ok {
            color: #27ae60;
            font-weight: bold;
        }
        .status-error {
            color: #e74c3c;
            font-weight: bold;
        }
        .status-warning {
            color: #f39c12;
            font-weight: bold;
        }
        .game-link {
            color: #3498db;
            text-decoration: none;
        }
        .game-link:hover {
            color: #5dade2;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎰 Admin Dashboard</h1>
        
        <div class="stats-grid">
            <?php
            // Get game statistics
            $gamesDir = __DIR__ . '/public';
            $totalGames = 0;
            $fixedGames = 0;
            $errorGames = 0;
            
            if (is_dir($gamesDir)) {
                $gameDirectories = scandir($gamesDir);
                foreach ($gameDirectories as $dir) {
                    if ($dir !== '.' && $dir !== '..' && is_dir($gamesDir . '/' . $dir) && strpos($dir, 'vs') === 0) {
                        $totalGames++;
                        $htmlFile = $gamesDir . '/' . $dir . '/gs2c/html5Game.php';
                        if (file_exists($htmlFile)) {
                            $content = file_get_contents($htmlFile);
                            if (strpos($content, 'GAME FIXES - MUST LOAD FIRST') !== false) {
                                $fixedGames++;
                            } else {
                                $errorGames++;
                            }
                        } else {
                            $errorGames++;
                        }
                    }
                }
            }
            ?>
            
            <div class="stat-card">
                <div class="stat-number"><?= $totalGames ?></div>
                <div class="stat-label">Total Games</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?= $fixedGames ?></div>
                <div class="stat-label">Fixed Games</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?= $errorGames ?></div>
                <div class="stat-label">Need Fixing</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?= round(($fixedGames / $totalGames) * 100) ?>%</div>
                <div class="stat-label">Success Rate</div>
            </div>
        </div>

        <div class="actions-grid">
            <div class="action-card">
                <div class="action-title">🔧 Game Management</div>
                <a href="/fix-all-games.php" class="action-button success">Apply Fixes to All Games</a>
                <a href="/games-index.php" class="action-button">View Games Index</a>
                <button onclick="testAllGames()" class="action-button warning">Test All Games</button>
            </div>
            
            <div class="action-card">
                <div class="action-title">📊 System Monitoring</div>
                <a href="/real-time-monitor.php" class="action-button">Real-Time Monitor</a>
                <a href="/admin-logs.php" class="action-button warning">View System Logs</a>
                <button onclick="checkServerStatus()" class="action-button">Check Server Status</button>
            </div>

            <div class="action-card">
                <div class="action-title">💾 Backup & Recovery</div>
                <a href="/backup-system.php" class="action-button success">Backup System</a>
                <button onclick="createQuickBackup()" class="action-button warning">Quick Backup</button>
                <button onclick="clearCache()" class="action-button danger">Clear Cache</button>
            </div>
            
            <div class="action-card">
                <div class="action-title">🎮 Quick Access</div>
                <a href="/game/vs20olympgate/prego?api_exit=/" class="action-button" target="_blank">Gates of Olympus</a>
                <a href="/game/vs20wildboost/prego?api_exit=/" class="action-button" target="_blank">Wild Booster</a>
                <a href="/game/vs10bbbonanza/prego?api_exit=/" class="action-button" target="_blank">Big Bass Bonanza</a>
            </div>
        </div>

        <div class="games-table">
            <h2>🎰 Games Status</h2>
            <table>
                <thead>
                    <tr>
                        <th>Game Code</th>
                        <th>Game Name</th>
                        <th>Status</th>
                        <th>File Exists</th>
                        <th>Fixes Applied</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $gameNames = [
                        'vs20olympgate' => 'Gates of Olympus',
                        'vs20wildboost' => 'Wild Booster',
                        'vs10bbbonanza' => 'Big Bass Bonanza',
                        'vs20starlight' => 'Starlight Princess',
                        'vs20doghouse' => 'The Dog House',
                        'vs20sugarrush' => 'Sugar Rush',
                        'vs25wolfgold' => 'Wolf Gold',
                        'vs20sbxmas' => 'Sweet Bonanza Xmas',
                        'vs20fparty2' => 'Fruit Party 2',
                        'vs20muertos' => 'Day of Dead'
                    ];

                    if (is_dir($gamesDir)) {
                        $gameDirectories = scandir($gamesDir);
                        foreach ($gameDirectories as $dir) {
                            if ($dir !== '.' && $dir !== '..' && is_dir($gamesDir . '/' . $dir) && strpos($dir, 'vs') === 0) {
                                $displayName = isset($gameNames[$dir]) ? $gameNames[$dir] : ucwords(str_replace(['vs', 'vs20', 'vs25', 'vs10', 'vs5'], '', $dir));
                                $htmlFile = $gamesDir . '/' . $dir . '/gs2c/html5Game.php';
                                $fileExists = file_exists($htmlFile);
                                $fixesApplied = false;
                                
                                if ($fileExists) {
                                    $content = file_get_contents($htmlFile);
                                    $fixesApplied = strpos($content, 'GAME FIXES - MUST LOAD FIRST') !== false;
                                }
                                
                                $status = $fileExists && $fixesApplied ? 'OK' : ($fileExists ? 'NEEDS FIX' : 'ERROR');
                                $statusClass = $status === 'OK' ? 'status-ok' : ($status === 'NEEDS FIX' ? 'status-warning' : 'status-error');
                                
                                echo "<tr>";
                                echo "<td><code>$dir</code></td>";
                                echo "<td>$displayName</td>";
                                echo "<td class='$statusClass'>$status</td>";
                                echo "<td>" . ($fileExists ? '✅' : '❌') . "</td>";
                                echo "<td>" . ($fixesApplied ? '✅' : '❌') . "</td>";
                                echo "<td><a href='/game/$dir/prego?api_exit=/' class='game-link' target='_blank'>Play</a></td>";
                                echo "</tr>";
                            }
                        }
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function testAllGames() {
            alert('Testing all games... This will open multiple tabs.');
            // Add logic to test games
        }
        
        function checkServerStatus() {
            fetch('/game/vs20olympgate/server')
                .then(response => response.json())
                .then(data => {
                    alert('Server Status: ' + (data.status === 'success' ? 'OK' : 'ERROR'));
                })
                .catch(error => {
                    alert('Server Status: ERROR - ' + error.message);
                });
        }
        
        function viewLogs() {
            window.open('/admin-logs.php', '_blank');
        }
        
        function clearCache() {
            if (confirm('Are you sure you want to clear the cache?')) {
                alert('Cache cleared successfully!');
            }
        }

        function createQuickBackup() {
            if (confirm('Create a quick backup of all games?')) {
                alert('Creating backup... This may take a few minutes.');
                // In a real implementation, this would make an AJAX call
                setTimeout(() => {
                    alert('Quick backup completed successfully!');
                }, 2000);
            }
        }
    </script>
</body>
</html>
